services:
  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_storage:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
    networks:
      - vector-poc-network

  vector-poc-api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      - OpenAI__ApiKey=${OPENAI_API_KEY}
      - Qdrant__Host=qdrant
      - Qdrant__Port=6333
    depends_on:
      - qdrant
    networks:
      - vector-poc-network

volumes:
  qdrant_storage:

networks:
  vector-poc-network:
    driver: bridge
