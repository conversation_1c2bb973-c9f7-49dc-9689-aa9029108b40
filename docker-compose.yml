services:
  qdrant:
    image: qdrant/qdrant:latest
    container_name: vector-poc-qdrant
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_storage:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
      - QDRANT__LOG_LEVEL=INFO
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    networks:
      - vector-poc-network

  vector-poc-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: vector-poc-api
    ports:
      - "8080:8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      - OpenAI__ApiKey=${OPENAI_API_KEY:-your-openai-api-key-here}
      - Qdrant__Host=qdrant
      - Qdrant__Port=6333
      - Qdrant__UseHttps=false
      - Qdrant__CollectionName=knowledge_base
      - Qdrant__VectorSize=1536
    depends_on:
      qdrant:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
    networks:
      - vector-poc-network

volumes:
  qdrant_storage:

networks:
  vector-poc-network:
    driver: bridge
