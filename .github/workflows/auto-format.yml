name: Auto Format

on:
  issue_comment:
    types: [created]

jobs:
  format:
    name: Auto Format Code
    runs-on: ubuntu-latest
    if: github.event.issue.pull_request && contains(github.event.comment.body, '/format')

    steps:
    - name: Get PR branch
      uses: xt0rted/pull-request-comment-branch@v2
      id: comment-branch

    - name: Checkout PR branch
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        ref: ${{ steps.comment-branch.outputs.head_ref }}

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '9.0.x'

    - name: Restore dependencies
      run: dotnet restore

    - name: Format code
      run: dotnet format

    - name: Check for changes
      id: verify-changed-files
      run: |
        if [ -n "$(git status --porcelain)" ]; then
          echo "changed=true" >> $GITHUB_OUTPUT
        else
          echo "changed=false" >> $GITHUB_OUTPUT
        fi

    - name: Commit changes
      if: steps.verify-changed-files.outputs.changed == 'true'
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add .
        git commit -m "Auto-format code"
        git push

    - name: Comment on PR
      uses: actions/github-script@v7
      with:
        script: |
          const { data: comments } = await github.rest.issues.listComments({
            owner: context.repo.owner,
            repo: context.repo.repo,
            issue_number: context.issue.number,
          });
          
          const botComment = comments.find(comment => 
            comment.user.type === 'Bot' && comment.body.includes('🤖 Auto-format')
          );
          
          const message = '${{ steps.verify-changed-files.outputs.changed }}' === 'true' 
            ? '🤖 Auto-format completed! Code has been formatted and committed.'
            : '🤖 Auto-format completed! No formatting changes were needed.';
          
          if (botComment) {
            github.rest.issues.updateComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              comment_id: botComment.id,
              body: message
            });
          } else {
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: message
            });
          }
