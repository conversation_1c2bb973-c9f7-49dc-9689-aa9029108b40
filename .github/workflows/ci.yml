name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  build-and-test:
    name: Build and Test
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '9.0.x'

    - name: Cache NuGet packages
      uses: actions/cache@v4
      with:
        path: ~/.nuget/packages
        key: ${{ runner.os }}-nuget-${{ hashFiles('**/packages.lock.json') }}
        restore-keys: |
          ${{ runner.os }}-nuget-

    - name: Restore dependencies
      run: dotnet restore

    - name: Build solution
      run: dotnet build --configuration Release --no-restore

    - name: Run Domain tests
      run: dotnet test tests/Domain.UnitTests/ --configuration Release --no-build --verbosity normal --logger trx --results-directory TestResults/

    - name: Run Application tests
      run: dotnet test tests/Application.UnitTests/ --configuration Release --no-build --verbosity normal --logger trx --results-directory TestResults/

    - name: Publish test results
      uses: dorny/test-reporter@v1
      if: success() || failure()
      with:
        name: .NET Tests
        path: TestResults/*.trx
        reporter: dotnet-trx

    - name: Build Docker image
      run: docker build -t vector-poc-service:${{ github.sha }} .

    - name: Test Docker image
      run: |
        docker run --rm -d -p 8080:8080 --name test-container vector-poc-service:${{ github.sha }}
        sleep 10
        curl -f http://localhost:8080/api/v1/health || exit 1
        docker stop test-container
