using Application.DTOs;
using Application.Features.Queries.ProcessQuery;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Api.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
public class QueryController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<QueryController> _logger;

    public QueryController(IMediator mediator, ILogger<QueryController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Processa uma consulta usando RAG (Retrieval-Augmented Generation)
    /// </summary>
    /// <param name="request">A consulta a ser processada</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resposta gerada com base na base de conhecimento</returns>
    [HttpPost("ask")]
    [ProducesResponseType(typeof(QueryResponseDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<QueryResponseDto>> ProcessQuery(
        [FromBody] QueryRequestDto request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Processing query: {Query}", request.Query);

            var command = new ProcessQueryCommand(request.Query);
            var response = await _mediator.Send(command, cancellationToken);

            _logger.LogInformation("Successfully processed query {QueryId}", response.QueryId);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process query: {Query}", request.Query);
            return StatusCode(StatusCodes.Status500InternalServerError, 
                new { message = "An error occurred while processing your query." });
        }
    }

    /// <summary>
    /// Endpoint de health check
    /// </summary>
    [HttpGet("health")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public ActionResult<object> HealthCheck()
    {
        return Ok(new { status = "healthy", timestamp = DateTime.UtcNow });
    }
}
