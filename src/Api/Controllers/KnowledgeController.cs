using Application.DTOs;
using Application.Features.Knowledge.CreateKnowledgeItem;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Api.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
public class KnowledgeController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<KnowledgeController> _logger;

    public KnowledgeController(IMediator mediator, ILogger<KnowledgeController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Adiciona um novo item à base de conhecimento
    /// </summary>
    /// <param name="request">Dados do item de conhecimento</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Item de conhecimento criado</returns>
    [HttpPost]
    [ProducesResponseType(typeof(KnowledgeItemDto), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<KnowledgeItemDto>> CreateKnowledgeItem(
        [FromBody] CreateKnowledgeItemDto request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating knowledge item with category: {Category}", request.Category);

            var command = new CreateKnowledgeItemCommand(
                request.Text,
                request.Category,
                request.Metadata);

            var response = await _mediator.Send(command, cancellationToken);

            _logger.LogInformation("Successfully created knowledge item {Id}", response.Id);

            return CreatedAtAction(nameof(CreateKnowledgeItem), new { id = response.Id }, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create knowledge item");
            return StatusCode(StatusCodes.Status500InternalServerError, 
                new { message = "An error occurred while creating the knowledge item." });
        }
    }
}
