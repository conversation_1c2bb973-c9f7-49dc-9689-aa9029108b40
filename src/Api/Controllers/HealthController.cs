using Microsoft.AspNetCore.Mvc;

namespace Api.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
public class HealthController : ControllerBase
{
    private readonly ILogger<HealthController> _logger;

    public HealthController(ILogger<HealthController> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Health check endpoint
    /// </summary>
    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public ActionResult<object> Get()
    {
        _logger.LogInformation("Health check requested");
        
        return Ok(new
        {
            status = "healthy",
            timestamp = DateTime.UtcNow,
            version = "1.0.0",
            service = "Vector POC Service",
            description = "RAG service with Clean Architecture and DDD"
        });
    }

    /// <summary>
    /// Detailed health check with dependencies status
    /// </summary>
    [HttpGet("detailed")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public ActionResult<object> GetDetailed()
    {
        _logger.LogInformation("Detailed health check requested");
        
        return Ok(new
        {
            status = "healthy",
            timestamp = DateTime.UtcNow,
            version = "1.0.0",
            service = "Vector POC Service",
            dependencies = new
            {
                qdrant = new
                {
                    status = "not_configured",
                    message = "Qdrant client not initialized for development"
                },
                openai = new
                {
                    status = "not_configured", 
                    message = "OpenAI API key not configured"
                }
            },
            features = new[]
            {
                "Clean Architecture",
                "Domain-Driven Design",
                "RAG (Retrieval-Augmented Generation)",
                "Vector Embeddings",
                "Semantic Search",
                "OpenAI Integration",
                "Qdrant Vector Database"
            }
        });
    }
}
