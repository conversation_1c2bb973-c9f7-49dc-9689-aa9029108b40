using Application.Features.Knowledge.CreateKnowledgeItem;
using MediatR;

namespace Application.Services;

public class KnowledgeSeederService
{
    private readonly IMediator _mediator;

    public KnowledgeSeederService(IMediator mediator)
    {
        _mediator = mediator;
    }

    public async Task SeedCleanArchitectureKnowledgeAsync(CancellationToken cancellationToken = default)
    {
        var knowledgeItems = GetCleanArchitectureKnowledge();

        foreach (var item in knowledgeItems)
        {
            var command = new CreateKnowledgeItemCommand(
                item.Text,
                item.Category,
                item.Metadata
            );

            await _mediator.Send(command, cancellationToken);
        }
    }

    private static List<(string Text, string Category, Dictionary<string, string> Metadata)> GetCleanArchitectureKnowledge()
    {
        return new List<(string, string, Dictionary<string, string>)>
        {
            (
                "Clean Architecture é uma arquitetura de software proposta por Robert <PERSON> (Uncle Bob) que organiza o código em camadas concêntricas. O princípio fundamental é que as dependências devem apontar sempre para dentro, em direção às regras de negócio. As camadas externas podem depender das internas, mas nunca o contrário.",
                "Clean Architecture",
                new Dictionary<string, string> { { "author", "Robert <PERSON> Martin" }, { "concept", "dependency-rule" } }
            ),
            (
                "Domain-Driven Design (DDD) é uma abordagem para desenvolvimento de software que coloca o domínio do negócio no centro do design. Enfatiza a colaboração entre especialistas técnicos e de domínio para criar um modelo que reflita a realidade do negócio. Conceitos principais incluem Entities, Value Objects, Aggregates, Repositories e Domain Services.",
                "DDD",
                new Dictionary<string, string> { { "author", "Eric Evans" }, { "concept", "domain-modeling" } }
            ),
            (
                "Entities são objetos que possuem identidade única e continuidade ao longo do tempo. Em DDD, uma Entity é definida não pelos seus atributos, mas pela sua identidade. Duas entities são consideradas iguais se possuem a mesma identidade, mesmo que seus atributos sejam diferentes.",
                "DDD",
                new Dictionary<string, string> { { "concept", "entity" }, { "pattern", "domain-object" } }
            ),
            (
                "Value Objects são objetos imutáveis que são definidos pelos seus atributos, não por identidade. Dois Value Objects são iguais se todos os seus atributos são iguais. Exemplos incluem Money, Address, DateRange. Eles encapsulam lógica de domínio e validações.",
                "DDD",
                new Dictionary<string, string> { { "concept", "value-object" }, { "pattern", "domain-object" } }
            ),
            (
                "Aggregates são clusters de objetos de domínio (Entities e Value Objects) que são tratados como uma unidade para mudanças de dados. Cada Aggregate tem uma raiz (Aggregate Root) que é a única Entity através da qual objetos externos podem referenciar qualquer objeto dentro do Aggregate.",
                "DDD",
                new Dictionary<string, string> { { "concept", "aggregate" }, { "pattern", "consistency-boundary" } }
            ),
            (
                "Repositories fornecem uma interface para acessar Aggregates, encapsulando a lógica de acesso a dados. Eles simulam uma coleção de objetos em memória, permitindo que o domínio permaneça independente da infraestrutura de persistência.",
                "DDD",
                new Dictionary<string, string> { { "concept", "repository" }, { "pattern", "data-access" } }
            ),
            (
                "Domain Services contêm lógica de domínio que não pertence naturalmente a uma Entity ou Value Object. Eles são stateless e focam em operações que envolvem múltiplos objetos de domínio ou cálculos complexos.",
                "DDD",
                new Dictionary<string, string> { { "concept", "domain-service" }, { "pattern", "domain-logic" } }
            ),
            (
                "A camada de Application em Clean Architecture contém os casos de uso da aplicação. Ela orquestra o fluxo de dados entre as camadas externas e o domínio, mas não contém regras de negócio. Use patterns como Command/Query, Mediator e Application Services.",
                "Clean Architecture",
                new Dictionary<string, string> { { "layer", "application" }, { "pattern", "use-case" } }
            ),
            (
                "A camada de Infrastructure implementa as interfaces definidas nas camadas internas. Inclui acesso a dados, serviços externos, frameworks e bibliotecas. Esta camada deve ser facilmente substituível sem afetar as regras de negócio.",
                "Clean Architecture",
                new Dictionary<string, string> { { "layer", "infrastructure" }, { "pattern", "implementation" } }
            ),
            (
                "CQRS (Command Query Responsibility Segregation) separa operações de leitura (queries) das operações de escrita (commands). Commands modificam estado e não retornam dados. Queries retornam dados mas não modificam estado. Isso permite otimizações específicas para cada tipo de operação.",
                "CQRS",
                new Dictionary<string, string> { { "pattern", "cqrs" }, { "concept", "separation-of-concerns" } }
            ),
            (
                "Event Sourcing é um padrão onde mudanças de estado são armazenadas como uma sequência de eventos. O estado atual é derivado pela reprodução de todos os eventos. Oferece auditoria completa, capacidade de replay e facilita a implementação de CQRS.",
                "Event Sourcing",
                new Dictionary<string, string> { { "pattern", "event-sourcing" }, { "concept", "state-management" } }
            ),
            (
                "Dependency Injection é um padrão que implementa Inversion of Control, onde dependências são fornecidas externamente ao invés de serem criadas internamente. Facilita testes, reduz acoplamento e melhora a flexibilidade do código.",
                "Design Patterns",
                new Dictionary<string, string> { { "pattern", "dependency-injection" }, { "principle", "ioc" } }
            )
        };
    }
}
