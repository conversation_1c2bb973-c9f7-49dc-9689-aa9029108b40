namespace Application.DTOs;

public record QueryRequestDto
{
    public string Query { get; init; } = string.Empty;
}

public record QueryResponseDto
{
    public Guid QueryId { get; init; }
    public string Query { get; init; } = string.Empty;
    public string Response { get; init; } = string.Empty;
    public List<KnowledgeItemDto> RelevantKnowledge { get; init; } = new();
    public DateTime ProcessedAt { get; init; }
}

public record KnowledgeItemDto
{
    public Guid Id { get; init; }
    public string Text { get; init; } = string.Empty;
    public string Category { get; init; } = string.Empty;
    public Dictionary<string, string> Metadata { get; init; } = new();
    public double RelevanceScore { get; init; }
}

public record CreateKnowledgeItemDto
{
    public string Text { get; init; } = string.Empty;
    public string Category { get; init; } = string.Empty;
    public Dictionary<string, string> Metadata { get; init; } = new();
}
