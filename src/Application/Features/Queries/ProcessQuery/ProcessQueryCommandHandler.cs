using Application.DTOs;
using Ardalis.GuardClauses;
using Domain.Entities;
using Domain.Interfaces;
using MediatR;

namespace Application.Features.Queries.ProcessQuery;

public class ProcessQueryCommandHandler : IRequestHandler<ProcessQueryCommand, QueryResponseDto>
{
    private readonly IQueryRepository _queryRepository;
    private readonly IKnowledgeRepository _knowledgeRepository;
    private readonly IEmbeddingService _embeddingService;
    private readonly ILanguageModelService _languageModelService;

    public ProcessQueryCommandHandler(
        IQueryRepository queryRepository,
        IKnowledgeRepository knowledgeRepository,
        IEmbeddingService embeddingService,
        ILanguageModelService languageModelService)
    {
        _queryRepository = Guard.Against.Null(queryRepository);
        _knowledgeRepository = Guard.Against.Null(knowledgeRepository);
        _embeddingService = Guard.Against.Null(embeddingService);
        _languageModelService = Guard.Against.Null(languageModelService);
    }

    public async Task<QueryResponseDto> Handle(ProcessQueryCommand request, CancellationToken cancellationToken)
    {
        // Create and save query
        var query = Query.Create(request.Query);
        await _queryRepository.AddAsync(query, cancellationToken);

        // Generate embedding for the query
        var queryEmbedding = await _embeddingService.GenerateEmbeddingAsync(request.Query, cancellationToken);
        query.SetEmbedding(queryEmbedding);

        // Search for similar knowledge items
        var relevantKnowledge = await _knowledgeRepository.SearchSimilarAsync(
            queryEmbedding, 
            limit: 5, 
            threshold: 0.7, 
            cancellationToken);

        var knowledgeList = relevantKnowledge.ToList();

        // Generate response using LLM
        var response = await _languageModelService.GenerateResponseAsync(
            request.Query, 
            knowledgeList, 
            cancellationToken);

        // Update query with response
        var relevantIds = knowledgeList.Select(k => k.Id).ToList();
        query.SetResponse(response, relevantIds);
        await _queryRepository.UpdateAsync(query, cancellationToken);

        // Map to DTO
        return new QueryResponseDto
        {
            QueryId = query.Id,
            Query = query.Text,
            Response = query.Response!,
            RelevantKnowledge = knowledgeList.Select(k => new KnowledgeItemDto
            {
                Id = k.Id,
                Text = k.Content.Text,
                Category = k.Content.Category,
                Metadata = k.Content.Metadata,
                RelevanceScore = k.RelevanceScore
            }).ToList(),
            ProcessedAt = query.ProcessedAt!.Value
        };
    }
}
