using FluentValidation;

namespace Application.Features.Queries.ProcessQuery;

public class ProcessQueryCommandValidator : AbstractValidator<ProcessQueryCommand>
{
    public ProcessQueryCommandValidator()
    {
        RuleFor(x => x.Query)
            .NotEmpty()
            .WithMessage("Query cannot be empty")
            .MinimumLength(3)
            .WithMessage("Query must be at least 3 characters long")
            .MaximumLength(1000)
            .WithMessage("Query cannot exceed 1000 characters");
    }
}
