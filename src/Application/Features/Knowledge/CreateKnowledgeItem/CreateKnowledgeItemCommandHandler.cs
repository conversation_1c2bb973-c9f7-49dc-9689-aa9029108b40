using Application.DTOs;
using Ardalis.GuardClauses;
using Domain.Entities;
using Domain.Interfaces;
using Domain.ValueObjects;
using MediatR;

namespace Application.Features.Knowledge.CreateKnowledgeItem;

public class CreateKnowledgeItemCommandHandler : IRequestHandler<CreateKnowledgeItemCommand, KnowledgeItemDto>
{
    private readonly IKnowledgeRepository _knowledgeRepository;
    private readonly IEmbeddingService _embeddingService;

    public CreateKnowledgeItemCommandHandler(
        IKnowledgeRepository knowledgeRepository,
        IEmbeddingService embeddingService)
    {
        _knowledgeRepository = Guard.Against.Null(knowledgeRepository);
        _embeddingService = Guard.Against.Null(embeddingService);
    }

    public async Task<KnowledgeItemDto> Handle(CreateKnowledgeItemCommand request, CancellationToken cancellationToken)
    {
        // Create content value object
        var content = Content.Create(request.Text, request.Category, request.Metadata);
        
        // Create knowledge item
        var knowledgeItem = KnowledgeItem.Create(content);
        
        // Generate embedding
        var embedding = await _embeddingService.GenerateEmbeddingAsync(request.Text, cancellationToken);
        knowledgeItem.SetEmbedding(embedding);
        
        // Save to repository
        await _knowledgeRepository.AddAsync(knowledgeItem, cancellationToken);

        // Map to DTO
        return new KnowledgeItemDto
        {
            Id = knowledgeItem.Id,
            Text = knowledgeItem.Content.Text,
            Category = knowledgeItem.Content.Category,
            Metadata = knowledgeItem.Content.Metadata,
            RelevanceScore = knowledgeItem.RelevanceScore
        };
    }
}
