using FluentValidation;

namespace Application.Features.Knowledge.CreateKnowledgeItem;

public class CreateKnowledgeItemCommandValidator : AbstractValidator<CreateKnowledgeItemCommand>
{
    public CreateKnowledgeItemCommandValidator()
    {
        RuleFor(x => x.Text)
            .NotEmpty()
            .WithMessage("Text cannot be empty")
            .MinimumLength(10)
            .WithMessage("Text must be at least 10 characters long")
            .MaximumLength(5000)
            .WithMessage("Text cannot exceed 5000 characters");

        RuleFor(x => x.Category)
            .NotEmpty()
            .WithMessage("Category cannot be empty")
            .MaximumLength(100)
            .WithMessage("Category cannot exceed 100 characters");
    }
}
