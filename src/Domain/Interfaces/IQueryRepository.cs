using Domain.Entities;

namespace Domain.Interfaces;

public interface IQueryRepository
{
    Task<Query?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<Query>> GetRecentAsync(int limit = 50, CancellationToken cancellationToken = default);
    Task<Query> AddAsync(Query query, CancellationToken cancellationToken = default);
    Task UpdateAsync(Query query, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
}
