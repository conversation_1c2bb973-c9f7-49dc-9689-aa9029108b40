using Domain.Entities;
using Domain.ValueObjects;

namespace Domain.Interfaces;

public interface IKnowledgeRepository
{
    Task<KnowledgeItem?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<KnowledgeItem>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<KnowledgeItem>> GetByCategoryAsync(
        string category,
        CancellationToken cancellationToken = default
    );
    Task<List<KnowledgeItem>> SearchSimilarAsync(
        Vector queryVector,
        int limit = 10,
        double threshold = 0.7,
        CancellationToken cancellationToken = default
    );
    Task<KnowledgeItem> AddAsync(
        KnowledgeItem knowledgeItem,
        CancellationToken cancellationToken = default
    );
    Task UpdateAsync(KnowledgeItem knowledgeItem, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default);
}
