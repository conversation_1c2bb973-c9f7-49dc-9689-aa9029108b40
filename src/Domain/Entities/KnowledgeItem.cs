using Ardalis.GuardClauses;
using Domain.Common;
using Domain.ValueObjects;

namespace Domain.Entities;

public class KnowledgeItem : BaseEntity
{
    public Content Content { get; private set; }
    public Vector? Embedding { get; private set; }
    public double RelevanceScore { get; private set; }

    private KnowledgeItem()
    {
        Content = null!; // For EF Core
    }

    public KnowledgeItem(Content content)
    {
        Guard.Against.Null(content, nameof(content));
        Content = content;
        RelevanceScore = 0.0;
    }

    public static KnowledgeItem Create(Content content) => new(content);

    public void SetEmbedding(Vector embedding)
    {
        Guard.Against.Null(embedding, nameof(embedding));
        Embedding = embedding;
        SetUpdatedAt();
    }

    public void UpdateRelevanceScore(double score)
    {
        Guard.Against.OutOfRange(score, nameof(score), 0.0, 1.0);
        RelevanceScore = score;
        SetUpdatedAt();
    }

    public void UpdateContent(Content newContent)
    {
        Guard.Against.Null(newContent, nameof(newContent));
        Content = newContent;
        // Reset embedding when content changes
        Embedding = null;
        SetUpdatedAt();
    }

    public bool HasEmbedding => Embedding != null;

    public double CalculateSimilarity(Vector queryVector)
    {
        if (!HasEmbedding)
            return 0.0;

        return Embedding!.CosineSimilarity(queryVector);
    }
}
