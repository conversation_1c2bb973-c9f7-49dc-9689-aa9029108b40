using Ardalis.GuardClauses;
using Domain.Common;
using Domain.ValueObjects;

namespace Domain.Entities;

public class Query : BaseEntity
{
    public string Text { get; private set; }
    public Vector? Embedding { get; private set; }
    public string? Response { get; private set; }
    public List<Guid> RelevantKnowledgeItemIds { get; private set; }
    public DateTime? ProcessedAt { get; private set; }

    private Query()
    {
        Text = null!; // For EF Core
        RelevantKnowledgeItemIds = new List<Guid>();
    }

    public Query(string text)
        : this()
    {
        Guard.Against.NullOrWhiteSpace(text, nameof(text));
        Text = text.Trim();
    }

    public static Query Create(string text) => new(text);

    public void SetEmbedding(Vector embedding)
    {
        Guard.Against.Null(embedding, nameof(embedding));
        Embedding = embedding;
        SetUpdatedAt();
    }

    public void SetResponse(string response, List<Guid> relevantKnowledgeItemIds)
    {
        Guard.Against.NullOrWhiteSpace(response, nameof(response));
        Guard.Against.Null(relevantKnowledgeItemIds, nameof(relevantKnowledgeItemIds));

        Response = response.Trim();
        RelevantKnowledgeItemIds = relevantKnowledgeItemIds.ToList();
        ProcessedAt = DateTime.UtcNow;
        SetUpdatedAt();
    }

    public bool IsProcessed => !string.IsNullOrEmpty(Response) && ProcessedAt.HasValue;
    public bool HasEmbedding => Embedding != null;
}
