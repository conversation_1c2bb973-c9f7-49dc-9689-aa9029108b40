using Ardalis.GuardClauses;

namespace Domain.ValueObjects;

public record Content
{
    public string Text { get; }
    public string Category { get; }
    public Dictionary<string, string> Metadata { get; }

    public Content(string text, string category, Dictionary<string, string>? metadata = null)
    {
        Guard.Against.NullOrWhiteSpace(text, nameof(text));
        Guard.Against.NullOrWhiteSpace(category, nameof(category));
        
        Text = text.Trim();
        Category = category.Trim();
        Metadata = metadata ?? new Dictionary<string, string>();
    }

    public static Content Create(string text, string category, Dictionary<string, string>? metadata = null)
        => new(text, category, metadata);

    public Content WithMetadata(string key, string value)
    {
        Guard.Against.NullOrWhiteSpace(key, nameof(key));
        Guard.Against.Null(value, nameof(value));

        var newMetadata = new Dictionary<string, string>(Metadata)
        {
            [key] = value
        };

        return new Content(Text, Category, newMetadata);
    }
}
