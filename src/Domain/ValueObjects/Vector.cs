using Ardalis.GuardClauses;

namespace Domain.ValueObjects;

public record Vector
{
    public float[] Values { get; }
    public int Dimensions => Values.Length;

    public Vector(float[] values)
    {
        Guard.Against.Null(values, nameof(values));
        Guard.Against.NullOrEmpty(values, nameof(values));

        Values = values;
    }

    public static Vector Create(float[] values) => new(values);

    public double CosineSimilarity(Vector other)
    {
        Guard.Against.Null(other, nameof(other));
        if (other.Dimensions != Dimensions)
            throw new ArgumentException(
                "Vector dimensions must match for similarity calculation",
                nameof(other)
            );

        var dotProduct = 0.0;
        var magnitudeA = 0.0;
        var magnitudeB = 0.0;

        for (var i = 0; i < Dimensions; i++)
        {
            dotProduct += Values[i] * other.Values[i];
            magnitudeA += Values[i] * Values[i];
            magnitudeB += other.Values[i] * other.Values[i];
        }

        magnitudeA = Math.Sqrt(magnitudeA);
        magnitudeB = Math.Sqrt(magnitudeB);

        if (magnitudeA == 0.0 || magnitudeB == 0.0)
            return 0.0;

        return dotProduct / (magnitudeA * magnitudeB);
    }
}
