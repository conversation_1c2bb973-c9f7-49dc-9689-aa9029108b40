using Application.Common.Interfaces;
using Domain.Interfaces;
using Infrastructure.Configuration;
using Infrastructure.Repositories;
using Infrastructure.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Qdrant.Client;

namespace Infrastructure;

public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructure(
        this IServiceCollection services,
        IConfiguration configuration
    )
    {
        // Configuration
        services.Configure<OpenAISettings>(configuration.GetSection(OpenAISettings.SectionName));
        services.Configure<QdrantSettings>(configuration.GetSection(QdrantSettings.SectionName));

        // Qdrant Client
        services.AddSingleton<QdrantClient>(serviceProvider =>
        {
            var qdrantSettings = serviceProvider
                .GetRequiredService<IOptions<QdrantSettings>>()
                .Value;
            return new QdrantClient(
                qdrantSettings.Host,
                qdrantSettings.Port,
                https: qdrantSettings.UseHttps,
                apiKey: qdrantSettings.ApiKey
            );
        });

        // Services
        services.AddScoped<IEmbeddingService, OpenAIEmbeddingService>();
        services.AddScoped<ILanguageModelService, OpenAILanguageModelService>();

        // Repositories
        services.AddScoped<IKnowledgeRepository, QdrantKnowledgeRepository>();
        services.AddSingleton<IQueryRepository, InMemoryQueryRepository>();

        // Hosted Services (commented out for development without Qdrant)
        services.AddHostedService<QdrantInitializationService>();

        return services;
    }
}
