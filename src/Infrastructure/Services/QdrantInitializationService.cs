using Ardalis.GuardClauses;
using Infrastructure.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Qdrant.Client;
using Qdrant.Client.Grpc;

namespace Infrastructure.Services;

public class QdrantInitializationService : IHostedService
{
    private readonly QdrantClient _qdrantClient;
    private readonly QdrantSettings _settings;
    private readonly ILogger<QdrantInitializationService> _logger;

    public QdrantInitializationService(
        QdrantClient qdrantClient,
        IOptions<QdrantSettings> settings,
        ILogger<QdrantInitializationService> logger
    )
    {
        _qdrantClient = Guard.Against.Null(qdrantClient);
        _settings = Guard.Against.Null(settings?.Value);
        _logger = Guard.Against.Null(logger);
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation(
                "Initializing Qdrant collection: {CollectionName}",
                _settings.CollectionName
            );

            // Check if collection exists
            var collections = await _qdrantClient.ListCollectionsAsync(cancellationToken);
            var collectionExists = collections.Any(c => c == _settings.CollectionName);

            if (!collectionExists)
            {
                _logger.LogInformation(
                    "Creating Qdrant collection: {CollectionName}",
                    _settings.CollectionName
                );

                // Create collection
                await _qdrantClient.CreateCollectionAsync(
                    _settings.CollectionName,
                    new VectorParams
                    {
                        Size = (ulong)_settings.VectorSize,
                        Distance = Distance.Cosine,
                    },
                    cancellationToken: cancellationToken
                );

                _logger.LogInformation(
                    "Successfully created Qdrant collection: {CollectionName}",
                    _settings.CollectionName
                );
            }
            else
            {
                _logger.LogInformation(
                    "Qdrant collection already exists: {CollectionName}",
                    _settings.CollectionName
                );
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to initialize Qdrant collection: {CollectionName}",
                _settings.CollectionName
            );
            throw;
        }
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        _qdrantClient?.Dispose();
        return Task.CompletedTask;
    }
}
