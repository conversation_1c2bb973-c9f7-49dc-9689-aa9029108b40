using System.Text;
using Ardalis.GuardClauses;
using Domain.Entities;
using Domain.Interfaces;
using Infrastructure.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using OpenAI;
using OpenAI.Chat;

namespace Infrastructure.Services;

public class OpenAILanguageModelService : ILanguageModelService
{
    private readonly OpenAIClient _openAIClient;
    private readonly OpenAISettings _settings;
    private readonly ILogger<OpenAILanguageModelService> _logger;

    public OpenAILanguageModelService(
        IOptions<OpenAISettings> settings,
        ILogger<OpenAILanguageModelService> logger)
    {
        _settings = Guard.Against.Null(settings?.Value);
        _logger = Guard.Against.Null(logger);
        
        Guard.Against.NullOrWhiteSpace(_settings.ApiKey, nameof(_settings.ApiKey));
        
        _openAIClient = new OpenAIClient(_settings.ApiKey);
    }

    public async Task<string> GenerateResponseAsync(
        string query, 
        IEnumerable<KnowledgeItem> relevantKnowledge, 
        CancellationToken cancellationToken = default)
    {
        Guard.Against.NullOrWhiteSpace(query, nameof(query));
        Guard.Against.Null(relevantKnowledge, nameof(relevantKnowledge));

        try
        {
            var knowledgeList = relevantKnowledge.ToList();
            _logger.LogDebug("Generating response for query with {Count} relevant knowledge items", knowledgeList.Count);

            var systemPrompt = BuildSystemPrompt();
            var userPrompt = BuildUserPrompt(query, knowledgeList);

            var chatClient = _openAIClient.GetChatClient(_settings.ChatModel);
            
            var messages = new List<ChatMessage>
            {
                new SystemChatMessage(systemPrompt),
                new UserChatMessage(userPrompt)
            };

            var chatCompletion = await chatClient.CompleteChatAsync(
                messages,
                new ChatCompletionOptions
                {
                    MaxOutputTokenCount = _settings.MaxTokens,
                    Temperature = (float)_settings.Temperature
                },
                cancellationToken);

            var response = chatCompletion.Value.Content[0].Text;

            _logger.LogDebug("Successfully generated response with length: {Length}", response.Length);

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate response for query: {Query}", query);
            throw;
        }
    }

    private static string BuildSystemPrompt()
    {
        return """
            Você é um assistente especializado em Clean Architecture e Domain-Driven Design (DDD).
            
            Sua função é responder perguntas baseando-se no conhecimento fornecido sobre estes temas.
            
            Diretrizes:
            1. Use apenas as informações fornecidas no contexto para responder
            2. Se a informação não estiver disponível no contexto, diga que não possui informações suficientes
            3. Seja preciso e técnico, mas mantenha a resposta clara e didática
            4. Cite conceitos e padrões específicos quando relevante
            5. Organize a resposta de forma estruturada
            6. Responda em português brasileiro
            """;
    }

    private static string BuildUserPrompt(string query, List<KnowledgeItem> knowledgeItems)
    {
        var promptBuilder = new StringBuilder();
        
        promptBuilder.AppendLine("CONTEXTO RELEVANTE:");
        promptBuilder.AppendLine();

        foreach (var item in knowledgeItems)
        {
            promptBuilder.AppendLine($"[{item.Content.Category}]");
            promptBuilder.AppendLine(item.Content.Text);
            
            if (item.Content.Metadata.Any())
            {
                promptBuilder.AppendLine("Metadados: " + string.Join(", ", 
                    item.Content.Metadata.Select(m => $"{m.Key}: {m.Value}")));
            }
            
            promptBuilder.AppendLine();
        }

        promptBuilder.AppendLine("PERGUNTA:");
        promptBuilder.AppendLine(query);

        return promptBuilder.ToString();
    }
}
