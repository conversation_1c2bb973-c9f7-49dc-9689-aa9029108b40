using Ardalis.GuardClauses;
using Domain.Interfaces;
using Domain.ValueObjects;
using Infrastructure.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using OpenAI;
using OpenAI.Embeddings;

namespace Infrastructure.Services;

public class OpenAIEmbeddingService : IEmbeddingService
{
    private readonly OpenAIClient _openAIClient;
    private readonly OpenAISettings _settings;
    private readonly ILogger<OpenAIEmbeddingService> _logger;

    public OpenAIEmbeddingService(
        IOptions<OpenAISettings> settings,
        ILogger<OpenAIEmbeddingService> logger
    )
    {
        _settings = Guard.Against.Null(settings?.Value);
        _logger = Guard.Against.Null(logger);

        Guard.Against.NullOrWhiteSpace(_settings.ApiKey, nameof(_settings.ApiKey));

        _openAIClient = new OpenAIClient(_settings.ApiKey);
    }

    public async Task<Vector> GenerateEmbeddingAsync(
        string text,
        CancellationToken cancellationToken = default
    )
    {
        Guard.Against.NullOrWhiteSpace(text, nameof(text));

        try
        {
            _logger.LogDebug("Generating embedding for text with length: {Length}", text.Length);

            var embeddingClient = _openAIClient.GetEmbeddingClient(_settings.EmbeddingModel);
            var response = await embeddingClient.GenerateEmbeddingAsync(
                text,
                options: null,
                cancellationToken
            );

            var embedding = response.Value;
            var vector = Vector.Create(embedding.ToFloats().ToArray());

            _logger.LogDebug(
                "Successfully generated embedding with {Dimensions} dimensions",
                vector.Dimensions
            );

            return vector;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate embedding for text");
            throw;
        }
    }

    public async Task<IEnumerable<Vector>> GenerateEmbeddingsAsync(
        IEnumerable<string> texts,
        CancellationToken cancellationToken = default
    )
    {
        Guard.Against.Null(texts, nameof(texts));

        var textList = texts.ToList();
        if (!textList.Any())
            return Enumerable.Empty<Vector>();

        try
        {
            _logger.LogDebug("Generating embeddings for {Count} texts", textList.Count);

            var embeddingClient = _openAIClient.GetEmbeddingClient(_settings.EmbeddingModel);
            var response = await embeddingClient.GenerateEmbeddingsAsync(
                textList,
                options: null,
                cancellationToken
            );

            var vectors = response
                .Value.Select(embedding => Vector.Create(embedding.ToFloats().ToArray()))
                .ToList();

            _logger.LogDebug("Successfully generated {Count} embeddings", vectors.Count);

            return vectors;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate embeddings for texts");
            throw;
        }
    }
}
