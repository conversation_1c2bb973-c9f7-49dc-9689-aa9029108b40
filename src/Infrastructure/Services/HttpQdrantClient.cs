using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using Infrastructure.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Qdrant.Client.Grpc;

namespace Infrastructure.Services;

public class HttpQdrantClient
{
    private readonly HttpClient _httpClient;
    private readonly QdrantSettings _settings;
    private readonly ILogger<HttpQdrantClient> _logger;
    private readonly string _baseUrl;

    public HttpQdrantClient(
        HttpClient httpClient,
        IOptions<QdrantSettings> settings,
        ILogger<HttpQdrantClient> logger
    )
    {
        _httpClient = httpClient;
        _settings = settings.Value;
        _logger = logger;

        var protocol = _settings.UseHttps ? "https" : "http";
        _baseUrl = $"{protocol}://{_settings.Host}";
        if (_settings.Port != (_settings.UseHttps ? 443 : 80))
        {
            _baseUrl += $":{_settings.Port}";
        }

        // Configure HttpClient
        if (!string.IsNullOrEmpty(_settings.ApiKey))
        {
            _httpClient.DefaultRequestHeaders.Add("api-key", _settings.ApiKey);
        }
    }

    public async Task<IEnumerable<string>> ListCollectionsAsync(
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/collections", cancellationToken);
            response.EnsureSuccessStatusCode();

            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            _logger.LogDebug("Collections API response: {Content}", content);

            var result = JsonSerializer.Deserialize<CollectionsResponse>(content);
            _logger.LogDebug(
                "Deserialized result: {Result}",
                result?.Result?.Collections?.Count ?? 0
            );

            var collections =
                result?.Result?.Collections?.Select(c => c.Name) ?? Enumerable.Empty<string>();
            _logger.LogDebug(
                "Extracted collection names: {Collections}",
                string.Join(", ", collections)
            );

            return collections;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to list collections");
            throw;
        }
    }

    public async Task CreateCollectionAsync(
        string collectionName,
        VectorParams vectorParams,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            var request = new
            {
                vectors = new
                {
                    size = vectorParams.Size,
                    distance = vectorParams.Distance.ToString().ToLower(),
                },
            };

            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PutAsync(
                $"{_baseUrl}/collections/{collectionName}",
                content,
                cancellationToken
            );

            response.EnsureSuccessStatusCode();
            _logger.LogInformation(
                "Successfully created collection: {CollectionName}",
                collectionName
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create collection: {CollectionName}", collectionName);
            throw;
        }
    }

    public async Task UpsertAsync(
        string collectionName,
        IEnumerable<PointStruct> points,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            var request = new
            {
                points = points.Select(p => new
                {
                    id = p.Id.Uuid,
                    vector = p.Vectors.Vector.Data.ToArray(),
                    payload = p.Payload.ToDictionary(
                        kvp => kvp.Key,
                        kvp => kvp.Value.StringValue ?? kvp.Value.ToString()
                    ),
                }),
            };

            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PutAsync(
                $"{_baseUrl}/collections/{collectionName}/points",
                content,
                cancellationToken
            );

            response.EnsureSuccessStatusCode();
            _logger.LogDebug("Successfully upserted {Count} points", points.Count());
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to upsert points to collection: {CollectionName}",
                collectionName
            );
            throw;
        }
    }

    public async Task<IEnumerable<ScoredPoint>> SearchAsync(
        string collectionName,
        float[] vector,
        ulong limit = 10,
        float? scoreThreshold = null,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            var request = new
            {
                vector = vector,
                limit = limit,
                score_threshold = scoreThreshold,
                with_payload = true,
                with_vector = false,
            };

            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync(
                $"{_baseUrl}/collections/{collectionName}/points/search",
                content,
                cancellationToken
            );

            response.EnsureSuccessStatusCode();

            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            var result = JsonSerializer.Deserialize<SearchResponse>(responseContent);

            return result?.Result?.Select(r => new ScoredPoint
                {
                    Id = new PointId { Uuid = r.Id },
                    Score = r.Score,
                    Payload =
                    {
                        r.Payload.ToDictionary(
                            kvp => kvp.Key,
                            kvp => new Value { StringValue = kvp.Value.ToString() }
                        ),
                    },
                }) ?? Enumerable.Empty<ScoredPoint>();
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to search in collection: {CollectionName}",
                collectionName
            );
            throw;
        }
    }

    // Response DTOs
    private class CollectionsResponse
    {
        [JsonPropertyName("result")]
        public CollectionsResult? Result { get; set; }
    }

    private class CollectionsResult
    {
        [JsonPropertyName("collections")]
        public List<CollectionInfo>? Collections { get; set; }
    }

    private class CollectionInfo
    {
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;
    }

    private class SearchResponse
    {
        public List<SearchResult>? Result { get; set; }
    }

    private class SearchResult
    {
        public string Id { get; set; } = string.Empty;
        public float Score { get; set; }
        public Dictionary<string, object> Payload { get; set; } = new();
    }
}
