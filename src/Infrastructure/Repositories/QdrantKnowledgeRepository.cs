using Ardalis.GuardClauses;
using Domain.Entities;
using Domain.Interfaces;
using Infrastructure.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Qdrant.Client;
using Qdrant.Client.Grpc;
using DomainVector = Domain.ValueObjects.Vector;
using QdrantVector = Qdrant.Client.Grpc.Vector;

namespace Infrastructure.Repositories;

public class QdrantKnowledgeRepository : IKnowledgeRepository
{
    private readonly QdrantClient _qdrantClient;
    private readonly QdrantSettings _settings;
    private readonly ILogger<QdrantKnowledgeRepository> _logger;

    public QdrantKnowledgeRepository(
        IOptions<QdrantSettings> settings,
        ILogger<QdrantKnowledgeRepository> logger
    )
    {
        _settings = Guard.Against.Null(settings?.Value);
        _logger = Guard.Against.Null(logger);

        try
        {
            _qdrantClient = new QdrantClient(
                _settings.Host,
                _settings.Port,
                https: _settings.UseHttps,
                apiKey: _settings.ApiKey
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to initialize Qdrant client with host: {Host}:{Port}",
                _settings.Host,
                _settings.Port
            );
            throw;
        }
    }

    public async Task<KnowledgeItem?> GetByIdAsync(
        Guid id,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            var response = await _qdrantClient.RetrieveAsync(
                _settings.CollectionName,
                new[] { new PointId { Uuid = id.ToString() } },
                cancellationToken: cancellationToken
            );

            var point = response.FirstOrDefault();
            return point != null ? MapToKnowledgeItem(point) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get knowledge item by id: {Id}", id);
            throw;
        }
    }

    public async Task<IEnumerable<KnowledgeItem>> GetAllAsync(
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            var response = await _qdrantClient.ScrollAsync(
                _settings.CollectionName,
                limit: 1000,
                cancellationToken: cancellationToken
            );

            return response.Result.Select(MapToKnowledgeItem);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get all knowledge items");
            throw;
        }
    }

    public async Task<IEnumerable<KnowledgeItem>> GetByCategoryAsync(
        string category,
        CancellationToken cancellationToken = default
    )
    {
        Guard.Against.NullOrWhiteSpace(category, nameof(category));

        try
        {
            var filter = new Filter
            {
                Must =
                {
                    new Condition
                    {
                        Field = new FieldCondition
                        {
                            Key = "category",
                            Match = new Match { Text = category },
                        },
                    },
                },
            };

            var response = await _qdrantClient.ScrollAsync(
                _settings.CollectionName,
                filter: filter,
                limit: 1000,
                cancellationToken: cancellationToken
            );

            return response.Result.Select(MapToKnowledgeItem);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get knowledge items by category: {Category}", category);
            throw;
        }
    }

    public async Task<List<KnowledgeItem>> SearchSimilarAsync(
        DomainVector queryVector,
        int limit = 10,
        double threshold = 0.7,
        CancellationToken cancellationToken = default
    )
    {
        Guard.Against.Null(queryVector, nameof(queryVector));
        Guard.Against.NegativeOrZero(limit, nameof(limit));

        try
        {
            var response = await _qdrantClient.SearchAsync(
                _settings.CollectionName,
                queryVector.Values,
                limit: (ulong)limit,
                scoreThreshold: (float)threshold,
                cancellationToken: cancellationToken
            );

            return response
                .Select(result =>
                {
                    var item = MapToKnowledgeItem(result);
                    item.UpdateRelevanceScore(result.Score);
                    return item;
                })
                .ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to search similar knowledge items");
            throw;
        }
    }

    public async Task<KnowledgeItem> AddAsync(
        KnowledgeItem knowledgeItem,
        CancellationToken cancellationToken = default
    )
    {
        Guard.Against.Null(knowledgeItem, nameof(knowledgeItem));
        Guard.Against.Null(knowledgeItem.Embedding, nameof(knowledgeItem.Embedding));

        try
        {
            var point = new PointStruct
            {
                Id = new PointId { Uuid = knowledgeItem.Id.ToString() },
                Vectors = knowledgeItem.Embedding.Values,
                Payload =
                {
                    ["text"] = knowledgeItem.Content.Text,
                    ["category"] = knowledgeItem.Content.Category,
                    ["created_at"] = knowledgeItem.CreatedAt.ToString("O"),
                    ["metadata"] = System.Text.Json.JsonSerializer.Serialize(
                        knowledgeItem.Content.Metadata
                    ),
                },
            };

            await _qdrantClient.UpsertAsync(
                _settings.CollectionName,
                new[] { point },
                cancellationToken: cancellationToken
            );

            _logger.LogDebug("Successfully added knowledge item: {Id}", knowledgeItem.Id);
            return knowledgeItem;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add knowledge item: {Id}", knowledgeItem.Id);
            throw;
        }
    }

    public async Task UpdateAsync(
        KnowledgeItem knowledgeItem,
        CancellationToken cancellationToken = default
    )
    {
        Guard.Against.Null(knowledgeItem, nameof(knowledgeItem));

        try
        {
            // For Qdrant, update is the same as upsert
            await AddAsync(knowledgeItem, cancellationToken);
            _logger.LogDebug("Successfully updated knowledge item: {Id}", knowledgeItem.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update knowledge item: {Id}", knowledgeItem.Id);
            throw;
        }
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            // TODO: Implement delete functionality with correct Qdrant API
            _logger.LogWarning(
                "Delete functionality not yet implemented for knowledge item: {Id}",
                id
            );
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete knowledge item: {Id}", id);
            throw;
        }
    }

    public async Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            var item = await GetByIdAsync(id, cancellationToken);
            return item != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check if knowledge item exists: {Id}", id);
            throw;
        }
    }

    private static KnowledgeItem MapToKnowledgeItem(RetrievedPoint point)
    {
        var text = point.Payload["text"].StringValue;
        var category = point.Payload["category"].StringValue;

        var metadata = new Dictionary<string, string>();
        if (point.Payload.TryGetValue("metadata", out var metadataValue))
        {
            var metadataJson = metadataValue.StringValue;
            if (!string.IsNullOrEmpty(metadataJson))
            {
                metadata =
                    System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(
                        metadataJson
                    )
                    ?? new Dictionary<string, string>();
            }
        }

        var content = Domain.ValueObjects.Content.Create(text, category, metadata);
        var knowledgeItem = KnowledgeItem.Create(content);

        // Set embedding if available
        if (point.Vectors?.Vector?.Data?.Count > 0)
        {
            var embedding = DomainVector.Create(point.Vectors.Vector.Data.ToArray());
            knowledgeItem.SetEmbedding(embedding);
        }

        return knowledgeItem;
    }

    private static KnowledgeItem MapToKnowledgeItem(ScoredPoint point)
    {
        var text = point.Payload["text"].StringValue;
        var category = point.Payload["category"].StringValue;

        var metadata = new Dictionary<string, string>();
        if (point.Payload.TryGetValue("metadata", out var metadataValue))
        {
            var metadataJson = metadataValue.StringValue;
            if (!string.IsNullOrEmpty(metadataJson))
            {
                metadata =
                    System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(
                        metadataJson
                    )
                    ?? new Dictionary<string, string>();
            }
        }

        var content = Domain.ValueObjects.Content.Create(text, category, metadata);
        var knowledgeItem = KnowledgeItem.Create(content);

        // Set embedding if available
        if (point.Vectors?.Vector?.Data?.Count > 0)
        {
            var embedding = DomainVector.Create(point.Vectors.Vector.Data.ToArray());
            knowledgeItem.SetEmbedding(embedding);
        }

        return knowledgeItem;
    }
}
