using System.Collections.Concurrent;
using Ardalis.GuardClauses;
using Domain.Entities;
using Domain.Interfaces;

namespace Infrastructure.Repositories;

public class InMemoryQueryRepository : IQueryRepository
{
    private readonly ConcurrentDictionary<Guid, Query> _queries = new();

    public Task<Query?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        _queries.TryGetValue(id, out var query);
        return Task.FromResult(query);
    }

    public Task<IEnumerable<Query>> GetRecentAsync(int limit = 50, CancellationToken cancellationToken = default)
    {
        Guard.Against.NegativeOrZero(limit, nameof(limit));

        var recentQueries = _queries.Values
            .OrderByDescending(q => q.CreatedAt)
            .Take(limit)
            .AsEnumerable();

        return Task.FromResult(recentQueries);
    }

    public Task<Query> AddAsync(Query query, CancellationToken cancellationToken = default)
    {
        Guard.Against.Null(query, nameof(query));

        _queries.TryAdd(query.Id, query);
        return Task.FromResult(query);
    }

    public Task UpdateAsync(Query query, CancellationToken cancellationToken = default)
    {
        Guard.Against.Null(query, nameof(query));

        _queries.TryUpdate(query.Id, query, _queries[query.Id]);
        return Task.CompletedTask;
    }

    public Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        _queries.TryRemove(id, out _);
        return Task.CompletedTask;
    }
}
