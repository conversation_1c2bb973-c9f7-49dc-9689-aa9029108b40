namespace Infrastructure.Configuration;

public class OpenAISettings
{
    public const string SectionName = "OpenAI";
    
    public string Api<PERSON>ey { get; set; } = string.Empty;
    public string EmbeddingModel { get; set; } = "text-embedding-3-small";
    public string ChatModel { get; set; } = "gpt-3.5-turbo";
    public int MaxTokens { get; set; } = 1000;
    public double Temperature { get; set; } = 0.7;
}
