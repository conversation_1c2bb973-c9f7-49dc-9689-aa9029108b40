#!/bin/bash

# Vector POC Service Setup Script
# This script helps set up the development environment

set -e

echo "🚀 Vector POC Service Setup"
echo "=========================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

# Check if .NET is installed
check_dotnet() {
    if command -v dotnet &> /dev/null; then
        DOTNET_VERSION=$(dotnet --version)
        print_status ".NET SDK found: $DOTNET_VERSION"
        
        if [[ "$DOTNET_VERSION" == 9.* ]]; then
            print_status ".NET 9.0 is installed"
        else
            print_warning ".NET 9.0 is recommended, but $DOTNET_VERSION found"
        fi
    else
        print_error ".NET SDK not found. Please install .NET 9.0 SDK"
        echo "Download from: https://dotnet.microsoft.com/download"
        exit 1
    fi
}

# Check if Docker is installed
check_docker() {
    if command -v docker &> /dev/null; then
        print_status "Docker found"
        
        if docker info &> /dev/null; then
            print_status "Docker daemon is running"
        else
            print_error "Docker daemon is not running. Please start Docker"
            exit 1
        fi
    else
        print_error "Docker not found. Please install Docker"
        echo "Download from: https://www.docker.com/get-started"
        exit 1
    fi
}

# Check if Docker Compose is installed
check_docker_compose() {
    if command -v docker-compose &> /dev/null; then
        print_status "Docker Compose found"
    else
        print_error "Docker Compose not found. Please install Docker Compose"
        exit 1
    fi
}

# Restore .NET dependencies
restore_dependencies() {
    print_info "Restoring .NET dependencies..."
    dotnet restore
    print_status "Dependencies restored"
}

# Build the project
build_project() {
    print_info "Building the project..."
    dotnet build --configuration Release
    print_status "Project built successfully"
}

# Run tests
run_tests() {
    print_info "Running tests..."
    dotnet test --verbosity normal
    print_status "All tests passed"
}

# Create .env file if it doesn't exist
create_env_file() {
    if [ ! -f .env ]; then
        print_info "Creating .env file..."
        cat > .env << EOF
# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key-here

# Qdrant Configuration
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_USE_HTTPS=false
QDRANT_API_KEY=

# Application Configuration
ASPNETCORE_ENVIRONMENT=Development
ASPNETCORE_URLS=http://+:8080
EOF
        print_status ".env file created"
        print_warning "Please update the .env file with your actual API keys"
    else
        print_status ".env file already exists"
    fi
}

# Setup development environment
setup_dev_environment() {
    print_info "Setting up development environment..."
    
    # Create logs directory
    mkdir -p logs
    print_status "Logs directory created"
    
    # Create data directory for Qdrant
    mkdir -p data/qdrant
    print_status "Data directory created"
}

# Start services with Docker Compose
start_services() {
    print_info "Starting services with Docker Compose..."
    docker-compose up -d
    print_status "Services started"
    
    print_info "Waiting for services to be ready..."
    sleep 10
    
    # Check if services are healthy
    if curl -f http://localhost:6333/health &> /dev/null; then
        print_status "Qdrant is healthy"
    else
        print_warning "Qdrant might not be ready yet"
    fi
    
    if curl -f http://localhost:8080/api/v1/health &> /dev/null; then
        print_status "Vector POC Service is healthy"
    else
        print_warning "Vector POC Service might not be ready yet"
    fi
}

# Show service URLs
show_urls() {
    echo ""
    echo "🌐 Service URLs:"
    echo "==============="
    echo "• API: http://localhost:8080"
    echo "• Swagger: http://localhost:8080/swagger"
    echo "• Health Check: http://localhost:8080/api/v1/health"
    echo "• Qdrant: http://localhost:6333"
    echo ""
}

# Main setup function
main() {
    echo "Checking prerequisites..."
    check_dotnet
    check_docker
    check_docker_compose
    
    echo ""
    echo "Setting up project..."
    create_env_file
    setup_dev_environment
    restore_dependencies
    build_project
    run_tests
    
    echo ""
    echo "🐳 Starting services..."
    start_services
    
    show_urls
    
    echo -e "${GREEN}🎉 Setup completed successfully!${NC}"
    echo ""
    echo "Next steps:"
    echo "1. Update your .env file with real API keys"
    echo "2. Visit http://localhost:8080/swagger to explore the API"
    echo "3. Try the health check: curl http://localhost:8080/api/v1/health"
    echo ""
    echo "To stop services: docker-compose down"
    echo "To view logs: docker-compose logs -f"
}

# Run main function
main "$@"
