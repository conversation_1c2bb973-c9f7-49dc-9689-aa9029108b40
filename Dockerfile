FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copy project files
COPY ["src/Api/Api.csproj", "src/Api/"]
COPY ["src/Application/Application.csproj", "src/Application/"]
COPY ["src/Infrastructure/Infrastructure.csproj", "src/Infrastructure/"]
COPY ["src/Domain/Domain.csproj", "src/Domain/"]
COPY ["Directory.Build.props", "."]
COPY ["Directory.Packages.props", "."]
COPY ["global.json", "."]

# Restore dependencies
RUN dotnet restore "src/Api/Api.csproj"

# Copy everything else
COPY . .

# Build the project
WORKDIR /src/src/Api
RUN dotnet build "Api.csproj" -c Release -o /app/build

# Publish the project
RUN dotnet publish "Api.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS runtime
WORKDIR /app
EXPOSE 8080

# Create a non-root user
RUN adduser --disabled-password --gecos '' --shell /bin/bash --uid 1001 appuser

# Copy published files
COPY --from=build /app/publish .

# Change ownership to appuser
RUN chown -R appuser:appuser /app
USER appuser

# Set environment variables
ENV ASPNETCORE_URLS=http://+:8080
ENV ASPNETCORE_ENVIRONMENT=Production

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/api/v1/health || exit 1

# Start the application
ENTRYPOINT ["dotnet", "Api.dll"]
