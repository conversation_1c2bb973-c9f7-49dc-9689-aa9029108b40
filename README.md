# Vector POC Service

Um serviço de RAG (Retrieval-Augmented Generation) implementado com Clean Architecture e DDD, utilizando OpenAI embeddings, Qdrant como base de dados vetorial e GPT-3.5 Turbo para geração de respostas.

## 🏗️ Arquitetura

O projeto segue os princípios de Clean Architecture e Domain-Driven Design:

- **Domain**: Entidades, Value Objects e interfaces do domínio
- **Application**: Casos de uso, DTOs e lógica de aplicação
- **Infrastructure**: Implementações de repositórios e serviços externos
- **Api**: Controllers e configuração da API

## 🚀 Funcionalidades

- **Base de Conhecimento**: Armazena informações sobre Clean Architecture e DDD
- **Embeddings**: Gera embeddings usando OpenAI text-embedding-3-small
- **Busca Vetorial**: Utiliza Qdrant para busca por similaridade
- **Geração de Respostas**: Usa GPT-3.5 Turbo para gerar respostas contextualizadas
- **API REST**: Endpoints para consultas e gerenciamento da base de conhecimento

## 📋 Pré-requisitos

- .NET 9.0 SDK
- Docker e Docker Compose
- Chave da API OpenAI

## ⚙️ Configuração

1. **Clone o repositório**
2. **Configure a chave da OpenAI**:
   - Edite `src/Api/appsettings.json`
   - Substitua `your-openai-api-key-here` pela sua chave da API OpenAI

3. **Configure variáveis de ambiente** (opcional):

   ```bash
   export OPENAI_API_KEY=sua-chave-aqui
   ```

## 🐳 Executando com Docker

```bash
# Inicie os serviços
docker-compose up -d

# Verifique os logs
docker-compose logs -f vector-poc-api
```

## 🔧 Executando Localmente

1. **Inicie o Qdrant**:

   ```bash
   docker run -p 6333:6333 -p 6334:6334 qdrant/qdrant:latest
   ```

2. **Execute a aplicação**:

   ```bash
   cd src/Api
   dotnet run
   ```

## 📚 Endpoints da API

### Processar Consulta

```http
POST /api/v1/query/ask
Content-Type: application/json

{
  "query": "O que é Clean Architecture?"
}
```

**Resposta**:

```json
{
  "queryId": "guid",
  "query": "O que é Clean Architecture?",
  "response": "Clean Architecture é uma arquitetura de software...",
  "relevantKnowledge": [
    {
      "id": "guid",
      "text": "Clean Architecture é uma arquitetura...",
      "category": "Clean Architecture",
      "metadata": {},
      "relevanceScore": 0.95
    }
  ],
  "processedAt": "2024-01-01T12:00:00Z"
}
```

### Adicionar Conhecimento

```http
POST /api/v1/knowledge
Content-Type: application/json

{
  "text": "Novo conhecimento sobre arquitetura...",
  "category": "Clean Architecture",
  "metadata": {
    "author": "Autor",
    "source": "Livro"
  }
}
```

### Health Check

```http
GET /api/v1/query/health
```

## 🧪 Testando

```bash
# Executar todos os testes
dotnet test

# Executar testes específicos
dotnet test tests/Domain.UnitTests/
dotnet test tests/Application.UnitTests/
```

## 📖 Base de Conhecimento

O serviço vem pré-configurado com conhecimento sobre:

- **Clean Architecture**: Princípios, camadas e dependências
- **Domain-Driven Design**: Entities, Value Objects, Aggregates, Repositories
- **CQRS**: Command Query Responsibility Segregation
- **Event Sourcing**: Padrões de gerenciamento de estado
- **Dependency Injection**: Inversão de controle

## 🔍 Exemplos de Consultas

- "O que é Clean Architecture?"
- "Explique o conceito de Value Objects em DDD"
- "Como funciona o padrão Repository?"
- "Qual a diferença entre Entity e Value Object?"
- "O que é CQRS e quando usar?"

## 🛠️ Tecnologias Utilizadas

- **.NET 9.0**: Framework principal
- **OpenAI API**: Embeddings e geração de texto
- **Qdrant**: Base de dados vetorial
- **MediatR**: Padrão Mediator
- **FluentValidation**: Validação de dados
- **Swagger**: Documentação da API
- **Docker**: Containerização

## 📁 Estrutura do Projeto

```
vector-poc-service/
├── src/
│   ├── Api/                 # Controllers e configuração
│   ├── Application/         # Casos de uso e DTOs
│   ├── Domain/             # Entidades e interfaces
│   └── Infrastructure/     # Implementações externas
├── tests/
│   ├── Domain.UnitTests/
│   └── Application.UnitTests/
├── Dockerfile
├── docker-compose.yml
└── README.md
```

## 🔧 Configurações Avançadas

### OpenAI
- **EmbeddingModel**: text-embedding-3-small (1536 dimensões)
- **ChatModel**: gpt-3.5-turbo
- **MaxTokens**: 1000
- **Temperature**: 0.7

### Qdrant
- **Host**: localhost
- **Port**: 6333
- **CollectionName**: knowledge_base
- **VectorSize**: 1536
- **Distance**: Cosine

## 🤝 Contribuindo

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 🚀 Primeiros Passos

### 1. Verificar se a aplicação está funcionando

```bash
# Executar a aplicação
cd src/Api
dotnet run

# Acessar o Swagger
# http://localhost:5140/swagger

# Testar health check
curl http://localhost:5140/api/v1/health
```

### 2. Configurar OpenAI (Opcional)

1. Obtenha uma chave da API OpenAI em https://platform.openai.com/
2. Edite `src/Api/appsettings.Development.json`:
   ```json
   {
     "OpenAI": {
       "ApiKey": "sua-chave-da-openai-aqui"
     }
   }
   ```

### 3. Configurar Qdrant

#### Opção A: Docker (Recomendado)
```bash
# Iniciar Qdrant
docker run -p 6333:6333 -p 6334:6334 qdrant/qdrant:latest

# Ou usar docker-compose
docker-compose up qdrant
```

#### Opção B: Docker Compose Completo
```bash
# Configurar variável de ambiente
export OPENAI_API_KEY=sua-chave-aqui

# Iniciar todos os serviços
docker-compose up
```

### 4. Habilitar Funcionalidades Completas

Após configurar OpenAI e Qdrant, descomente as linhas em:

**`src/Infrastructure/DependencyInjection.cs`**:
```csharp
// Hosted Services
services.AddHostedService<QdrantInitializationService>();
```

**`src/Api/Program.cs`**:
```csharp
// Seed knowledge base on startup
using (var scope = app.Services.CreateScope())
{
    var seeder = scope.ServiceProvider.GetRequiredService<KnowledgeSeederService>();
    await seeder.SeedCleanArchitectureKnowledgeAsync();
}
```

## 🧪 Testando a API

### Health Check
```bash
curl http://localhost:5140/api/v1/health
```

### Consulta RAG (após configuração completa)
```bash
curl -X POST http://localhost:5140/api/v1/query/ask \
  -H "Content-Type: application/json" \
  -d '{"query": "O que é Clean Architecture?"}'
```

### Adicionar Conhecimento
```bash
curl -X POST http://localhost:5140/api/v1/knowledge \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Novo conhecimento sobre arquitetura...",
    "category": "Clean Architecture",
    "metadata": {
      "author": "Autor",
      "source": "Livro"
    }
  }'
```

## 🔧 Desenvolvimento

### Executar Testes
```bash
# Todos os testes
dotnet test

# Testes específicos
dotnet test tests/Domain.UnitTests/
dotnet test tests/Application.UnitTests/
```

### Build
```bash
# Build completo
dotnet build

# Build para produção
dotnet build -c Release
```

### Logs
Os logs são configurados para mostrar informações detalhadas em desenvolvimento:
- **Default**: Information
- **Microsoft.AspNetCore**: Warning
- **Infrastructure**: Debug

## 📄 Licença

Este projeto está sob a licença MIT.
