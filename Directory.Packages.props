<!-- For more info on central package management go to https://devblogs.microsoft.com/nuget/introducing-central-package-management/ -->
<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <AspnetVersion>9.0.0</AspnetVersion>
    <MicrosoftExtensionsVersion>9.0.0</MicrosoftExtensionsVersion>
  </PropertyGroup>
  <ItemGroup>
    <!-- Core packages -->
    <PackageVersion Include="Ardalis.GuardClauses" Version="4.6.0" />
    <PackageVersion Include="AutoMapper" Version="13.0.1" />
    <PackageVersion Include="FluentValidation.AspNetCore" Version="11.3.0" />
    <PackageVersion Include="FluentValidation.DependencyInjectionExtensions" Version="11.11.0" />
    <PackageVersion Include="MediatR" Version="12.4.1" />
    
    <!-- ASP.NET Core -->
    <PackageVersion Include="Microsoft.AspNetCore.OpenApi" Version="9.0.7" />
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="6.8.1" />
    
    <!-- OpenAI -->
    <PackageVersion Include="OpenAI" Version="2.1.0" />
    
    <!-- Qdrant -->
    <PackageVersion Include="Qdrant.Client" Version="1.12.0" />
    
    <!-- Configuration -->
    <PackageVersion Include="Microsoft.Extensions.Configuration.Json" Version="$(MicrosoftExtensionsVersion)" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="$(MicrosoftExtensionsVersion)" />
    <PackageVersion Include="Microsoft.Extensions.Hosting" Version="$(MicrosoftExtensionsVersion)" />
    <PackageVersion Include="Microsoft.Extensions.Logging" Version="9.0.8" />
    
    <!-- Testing -->
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.11.1" />
    <PackageVersion Include="nunit" Version="3.14.0" />
    <PackageVersion Include="NUnit.Analyzers" Version="3.10.0" />
    <PackageVersion Include="NUnit3TestAdapter" Version="4.6.0" />
    <PackageVersion Include="FluentAssertions" Version="6.12.0" />
    <PackageVersion Include="Moq" Version="4.20.72" />
    <PackageVersion Include="coverlet.collector" Version="6.0.2" />
  </ItemGroup>
</Project>
