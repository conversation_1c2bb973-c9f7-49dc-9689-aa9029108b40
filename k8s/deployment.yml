apiVersion: apps/v1
kind: Deployment
metadata:
  name: vector-poc-service-deployment
  labels:
    app: vector-poc-service
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: vector-poc-service
  template:
    metadata:
      labels:
        app: vector-poc-service
        version: v1
    spec:
      containers:
      - name: vector-poc-service
        image: gcr.io/highcapital-470117/vector-poc-service:latest
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Production"
        - name: ASPNETCORE_URLS
          value: "http://+:8080"
        - name: OpenAI__ApiKey
          valueFrom:
            secretKeyRef:
              name: vector-poc-service-secrets
              key: openai-api-key
        - name: Qdrant__Host
          valueFrom:
            configMapKeyRef:
              name: vector-poc-service-config
              key: qdrant-host
        - name: Qdrant__Port
          valueFrom:
            configMapKeyRef:
              name: vector-poc-service-config
              key: qdrant-port
        - name: Qdrant__UseHttps
          valueFrom:
            configMapKeyRef:
              name: vector-poc-service-config
              key: qdrant-use-https
        - name: Qdrant__ApiKey
          valueFrom:
            secretKeyRef:
              name: vector-poc-service-secrets
              key: qdrant-api-key
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/v1/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/v1/health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /api/v1/health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
      imagePullSecrets:
      - name: gcr-json-key
