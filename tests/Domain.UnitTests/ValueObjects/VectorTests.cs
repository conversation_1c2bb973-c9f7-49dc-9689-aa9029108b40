using Domain.ValueObjects;
using FluentAssertions;

namespace Domain.UnitTests.ValueObjects;

[TestFixture]
public class VectorTests
{
    [Test]
    public void Create_WithValidValues_ShouldCreateVector()
    {
        // Arrange
        var values = new float[] { 1.0f, 2.0f, 3.0f };

        // Act
        var vector = Vector.Create(values);

        // Assert
        vector.Should().NotBeNull();
        vector.Values.Should().BeEquivalentTo(values);
        vector.Dimensions.Should().Be(3);
    }

    [Test]
    public void Create_WithNullValues_ShouldThrowArgumentNullException()
    {
        // Arrange
        float[] values = null!;

        // Act & Assert
        var act = () => Vector.Create(values);
        act.Should().Throw<ArgumentNullException>();
    }

    [Test]
    public void Create_WithEmptyValues_ShouldThrowArgumentException()
    {
        // Arrange
        var values = Array.Empty<float>();

        // Act & Assert
        var act = () => Vector.Create(values);
        act.Should().Throw<ArgumentException>();
    }

    [Test]
    public void CosineSimilarity_WithIdenticalVectors_ShouldReturnOne()
    {
        // Arrange
        var values = new float[] { 1.0f, 2.0f, 3.0f };
        var vector1 = Vector.Create(values);
        var vector2 = Vector.Create(values);

        // Act
        var similarity = vector1.CosineSimilarity(vector2);

        // Assert
        similarity.Should().BeApproximately(1.0, 0.0001);
    }

    [Test]
    public void CosineSimilarity_WithOrthogonalVectors_ShouldReturnZero()
    {
        // Arrange
        var vector1 = Vector.Create(new float[] { 1.0f, 0.0f });
        var vector2 = Vector.Create(new float[] { 0.0f, 1.0f });

        // Act
        var similarity = vector1.CosineSimilarity(vector2);

        // Assert
        similarity.Should().BeApproximately(0.0, 0.0001);
    }

    [Test]
    public void CosineSimilarity_WithDifferentDimensions_ShouldThrowArgumentException()
    {
        // Arrange
        var vector1 = Vector.Create(new float[] { 1.0f, 2.0f });
        var vector2 = Vector.Create(new float[] { 1.0f, 2.0f, 3.0f });

        // Act & Assert
        var act = () => vector1.CosineSimilarity(vector2);
        act.Should().Throw<ArgumentException>()
            .WithMessage("*Vector dimensions must match*");
    }

    [Test]
    public void CosineSimilarity_WithNullVector_ShouldThrowArgumentNullException()
    {
        // Arrange
        var vector = Vector.Create(new float[] { 1.0f, 2.0f });
        Vector nullVector = null!;

        // Act & Assert
        var act = () => vector.CosineSimilarity(nullVector);
        act.Should().Throw<ArgumentNullException>();
    }
}
