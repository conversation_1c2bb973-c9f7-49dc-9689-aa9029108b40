# Deployment Guide

Este documento descreve como fazer deploy do Vector POC Service em diferentes ambientes.

## 🚀 Cloud Run (Recomendado para desenvolvimento)

### Pré-requisitos
- Conta no Google Cloud Platform
- Projeto GCP configurado
- Service Account com permissões necessárias

### Configuração de Secrets no GitHub

```bash
# Secrets necessários no GitHub Actions
GCP_PROJECT_ID=your-project-id
GCP_SERVICE_NAME=vector-poc-service
GCP_REGION=us-central1
GCP_SA_KEY={"type":"service_account",...}
GCP_MAX_INSTANCES=10
```

### Deploy Automático

O deploy é feito automaticamente quando:
- Push na branch `release`
- Trigger manual via GitHub Actions

### Deploy Manual

```bash
# Build da imagem
docker build -t gcr.io/your-project/vector-poc-service .

# Push para GCR
docker push gcr.io/your-project/vector-poc-service

# Deploy no Cloud Run
gcloud run deploy vector-poc-service \
  --image gcr.io/your-project/vector-poc-service \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --port 8080 \
  --memory 1Gi \
  --cpu 1
```

## ☸️ Kubernetes (Produção)

### Pré-requisitos
- Cluster Kubernetes configurado
- kubectl configurado
- Acesso ao Google Container Registry

### Configuração de Secrets

```bash
# Criar secrets no Kubernetes
kubectl create secret generic vector-poc-service-secrets \
  --from-literal=openai-api-key=your-openai-key \
  --from-literal=qdrant-api-key=your-qdrant-key

# Criar secret para GCR
kubectl create secret docker-registry gcr-json-key \
  --docker-server=gcr.io \
  --docker-username=_json_key \
  --docker-password="$(cat service-account-key.json)" \
  --docker-email=<EMAIL>
```

### Deploy Manual

```bash
# Aplicar todas as configurações
kubectl apply -f k8s/

# Verificar status
kubectl get pods -l app=vector-poc-service
kubectl get services
kubectl get ingress
```

### Monitoramento

```bash
# Logs
kubectl logs -f deployment/vector-poc-service-deployment

# Status do HPA
kubectl get hpa

# Status do VPA
kubectl get vpa

# Métricas
kubectl top pods -l app=vector-poc-service
```

## 🐳 Docker Local

### Desenvolvimento

```bash
# Build
docker build -t vector-poc-service .

# Run
docker run -p 8080:8080 \
  -e OpenAI__ApiKey=your-key \
  -e Qdrant__Host=your-qdrant-host \
  vector-poc-service
```

### Docker Compose

```bash
# Iniciar todos os serviços
docker-compose up

# Apenas o serviço principal
docker-compose up vector-poc-service
```

## 🔧 Configuração de Ambiente

### Variáveis de Ambiente

| Variável | Descrição | Exemplo |
|----------|-----------|---------|
| `ASPNETCORE_ENVIRONMENT` | Ambiente da aplicação | `Production` |
| `OpenAI__ApiKey` | Chave da API OpenAI | `sk-...` |
| `Qdrant__Host` | Host do Qdrant | `qdrant.example.com` |
| `Qdrant__Port` | Porta do Qdrant | `443` |
| `Qdrant__UseHttps` | Usar HTTPS | `true` |
| `Qdrant__ApiKey` | Chave da API Qdrant | `your-key` |

### Health Checks

A aplicação expõe endpoints de health check:

- `GET /api/v1/health` - Health check básico
- `GET /api/v1/health/detailed` - Health check detalhado

## 🔒 Segurança

### Secrets Management

- **Nunca** commite chaves de API no código
- Use secrets do Kubernetes ou variáveis de ambiente
- Rotacione chaves regularmente

### Network Security

- Configure ingress com TLS
- Use rate limiting
- Configure CORS adequadamente

## 📊 Monitoramento

### Métricas Disponíveis

- CPU e memória usage
- Request rate e latência
- Health check status
- Custom metrics da aplicação

### Alertas Recomendados

- High CPU/Memory usage
- Health check failures
- High error rate
- Response time degradation

## 🚨 Troubleshooting

### Problemas Comuns

1. **Pod não inicia**
   ```bash
   kubectl describe pod <pod-name>
   kubectl logs <pod-name>
   ```

2. **Secrets não encontrados**
   ```bash
   kubectl get secrets
   kubectl describe secret vector-poc-service-secrets
   ```

3. **Problemas de conectividade**
   ```bash
   kubectl exec -it <pod-name> -- curl http://localhost:8080/api/v1/health
   ```

### Logs

```bash
# Logs em tempo real
kubectl logs -f deployment/vector-poc-service-deployment

# Logs de todos os pods
kubectl logs -l app=vector-poc-service --tail=100
```
